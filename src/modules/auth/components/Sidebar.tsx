import { Link } from "@tanstack/react-router";
import { Calendar, Clock, FileCheck, Home, UserCog, Users } from "lucide-react";

export default function Sidebar() {
	return (
		<div className="h-full w-64 bg-base-300 shadow-lg">
			<div className="p-4">
				<h2 className="mb-6 font-bold text-2xl text-primary">Schedhold</h2>
				<ul className="menu menu-lg w-full rounded-box bg-base-200">
					<li>
						<Link
							to="/admin"
							className="flex items-center gap-3 hover:bg-base-300"
						>
							<Home className="h-5 w-5" />
							Home
						</Link>
					</li>
					<li>
						<Link
							to="/admin/clients"
							className="flex items-center gap-3 hover:bg-base-300"
						>
							<Users className="h-5 w-5" />
							Clientes
						</Link>
					</li>
					<li>
						<Link
							to="/admin/workers"
							className="flex items-center gap-3 hover:bg-base-300"
						>
							<UserCog className="h-5 w-5" />
							Trabajadores
						</Link>
					</li>
					<li>
						<Link
							to="/admin/schedules"
							className="flex items-center gap-3 hover:bg-base-300"
						>
							<Calendar className="h-5 w-5" />
							Horarios
						</Link>
					</li>
					<li>
						<Link
							to="/admin/sessions"
							className="flex items-center gap-3 hover:bg-base-300"
						>
							<FileCheck className="h-5 w-5" />
							Sesiones
						</Link>
					</li>
				</ul>
			</div>
		</div>
	);
}
