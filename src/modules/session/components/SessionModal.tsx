import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import ComboBox from "~/core/components/ComboBox";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import type { Client } from "~/modules/client/service/model/client";
import useCreateSession from "~/modules/session/hooks/use-create-session";
import type { Session } from "~/modules/session/service/model/session";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import type { Worker } from "~/modules/worker/service/model/worker";
import { sessionStore } from "../store/session";

interface SessionModalProps {
	isOpen: boolean;
	onClose: () => void;
	dayIndex: number;
	timeIndex: number;
	existingSession: Session | undefined;
}

export default function SessionModal({
	isOpen,
	onClose,
	dayIndex,
	timeIndex,
	existingSession,
}: SessionModalProps) {
	const svc = useService();
	const { mutate: createSession } = useCreateSession();
	const {
		selectedClient: preSelectedClient,
		selectedWorker: preSelectedWorker,
		selectedTurn,
	} = useStore(sessionStore);

	const turnId = selectedTurn?.id || "";

	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);

	const {
		data: clients,
		isError: clientsError,
		error: clientError,
		isPending: clientsPending,
	} = useQuery({
		...clientOptions(svc),
		enabled: isOpen,
	});

	const {
		data: workers,
		isError: workersError,
		error: workerError,
		isPending: workersPending,
	} = useQuery({
		...workerOptions(svc),
		enabled: isOpen,
	});

	// Set initial values when modal opens
	useEffect(() => {
		if (isOpen) {
			if (existingSession) {
				setSelectedClient(existingSession.client);
				setSelectedWorker(existingSession.worker);
			} else {
				setSelectedClient(preSelectedClient);
				setSelectedWorker(preSelectedWorker);
			}
		}
	}, [isOpen, existingSession, preSelectedClient, preSelectedWorker]);

	// Reset when modal closes
	useEffect(() => {
		if (!isOpen) {
			setSelectedClient(null);
			setSelectedWorker(null);
		}
	}, [isOpen]);

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	const clientOptions_data =
		clients?.map((client) => ({
			value: client.id,
			label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
		})) || [];

	const workerOptions_data =
		workers?.map((worker) => ({
			value: worker.id,
			label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
		})) || [];

	const handleClientChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const client = clients?.find((c) => c.id === option.value);
			setSelectedClient(client || null);
		} else {
			setSelectedClient(null);
		}
	};

	const handleWorkerChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const worker = workers?.find((w) => w.id === option.value);
			setSelectedWorker(worker || null);
		} else {
			setSelectedWorker(null);
		}
	};

	const handleSubmit = () => {
		let finalClientId: string;
		let finalWorkerId: string;

		if (preSelectedClient) {
			// Client was pre-selected from store, use worker from modal
			finalClientId = preSelectedClient.id;
			finalWorkerId = selectedWorker?.id || "";
		} else if (preSelectedWorker) {
			// Worker was pre-selected from store, use client from modal
			finalWorkerId = preSelectedWorker.id;
			finalClientId = selectedClient?.id || "";
		} else {
			// This shouldn't happen, but handle it just in case
			finalClientId = selectedClient?.id || "";
			finalWorkerId = selectedWorker?.id || "";
		}

		if (!finalClientId || !finalWorkerId) {
			toast.error("Debe seleccionar un cliente y un trabajador");
			return;
		}

		createSession(
			{
				clientId: finalClientId,
				workerId: finalWorkerId,
				turnId,
				day: dayIndex,
				time: timeIndex,
			},
			{
				onSuccess: () => {
					toast.success("Sesión creada exitosamente");
					onClose();
				},
				onError: (error) => {
					console.log(error);
					const { error: errorResult } = getErrorResult(error);
					toast.error(errorResult.message);
				},
			},
		);
	};

	const selectedClientOption = selectedClient
		? {
				value: selectedClient.id,
				label: `${selectedClient.person.name} ${selectedClient.person.fatherLastName} ${selectedClient.person.motherLastName}`,
				data: selectedClient,
			}
		: null;

	const selectedWorkerOption = selectedWorker
		? {
				value: selectedWorker.id,
				label: `${selectedWorker.person.name} ${selectedWorker.person.fatherLastName} ${selectedWorker.person.motherLastName}`,
				data: selectedWorker,
			}
		: null;

	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={onClose} />
				<h3 className="font-bold text-lg">
					{existingSession ? "Editar Sesión" : "Crear Sesión"}
				</h3>

				<div className="py-4">
					<p className="mb-4 text-base-content/70 text-sm">
						{days[dayIndex]} - Horario {timeIndex + 1}
					</p>

					{!existingSession && (preSelectedClient || preSelectedWorker) && (
						<div className="mb-4 rounded-lg bg-base-200 p-3">
							<p className="font-medium text-sm">
								{preSelectedClient
									? `Creando sesión para: ${preSelectedClient.person.name} ${preSelectedClient.person.fatherLastName}`
									: `Creando sesión con: ${preSelectedWorker?.person.name} ${preSelectedWorker?.person.fatherLastName}`}
							</p>
						</div>
					)}

					<div className="space-y-4">
						{/* Show Client Selection only if no worker was pre-selected or if editing existing session */}
						{(preSelectedWorker || existingSession) && (
							<div className="form-control">
								<label htmlFor="session-client-combobox" className="label">
									<span className="label-text">Cliente</span>
								</label>
								{clientsError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(clientError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={clientOptions_data}
										value={selectedClientOption}
										onChange={handleClientChange}
										placeholder="Buscar cliente..."
										isLoading={clientsPending}
										label="Cliente"
									/>
								)}
							</div>
						)}

						{/* Show Worker Selection only if no client was pre-selected or if editing existing session */}
						{(preSelectedClient || existingSession) && (
							<div className="form-control">
								<label htmlFor="session-worker-combobox" className="label">
									<span className="label-text">Trabajador</span>
								</label>
								{workersError ? (
									<div className="alert alert-error">
										<span>
											Error: {getErrorResult(workerError).error.message}
										</span>
									</div>
								) : (
									<ComboBox
										options={workerOptions_data}
										value={selectedWorkerOption}
										onChange={handleWorkerChange}
										placeholder="Buscar trabajador..."
										isLoading={workersPending}
									/>
								)}
							</div>
						)}

						{/* Show message when only one field is available */}
						{preSelectedClient && !existingSession && (
							<div className="alert alert-info">
								<span>
									Cliente seleccionado: {preSelectedClient.person.name}{" "}
									{preSelectedClient.person.fatherLastName}. Selecciona un
									trabajador para esta sesión.
								</span>
							</div>
						)}

						{preSelectedWorker && !existingSession && (
							<div className="alert alert-info">
								<span>
									Trabajador seleccionado: {preSelectedWorker.person.name}{" "}
									{preSelectedWorker.person.fatherLastName}. Selecciona un
									cliente para esta sesión.
								</span>
							</div>
						)}
					</div>
				</div>
				<div className="modal-action">
					<button type="button" className="btn btn-ghost" onClick={onClose}>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-primary"
						onClick={handleSubmit}
						disabled={!selectedClient || !selectedWorker}
					>
						{existingSession ? "Actualizar" : "Crear"} Sesión
					</button>
				</div>
			</div>
		</div>
	);
}
