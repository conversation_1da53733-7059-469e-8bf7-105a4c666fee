import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import ComboBox from "~/core/components/ComboBox";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientOptions } from "~/modules/client/hooks/client-options";
import { workerOptions } from "~/modules/worker/hooks/worker-options";
import { sessionActions, sessionStore } from "../store/session";

export default function ClientWorkerSelects() {
	const { selectedClient, selectedWorker } = useStore(sessionStore);
	const svc = useService();

	const {
		data: clients,
		isError: clientsError,
		error: clientError,
		isPending: clientsPending,
	} = useQuery(clientOptions(svc));

	const {
		data: workers,
		isError: workersError,
		error: workerError,
		isPending: workersPending,
	} = useQuery(workerOptions(svc));

	useEffect(() => {
		if (clientError) {
			console.log(getErrorResult(clientError).error);
		}
		if (workerError) {
			console.log(getErrorResult(workerError).error);
		}
	}, [clientError, workerError]);

	const clientOptions_data =
		clients?.map((client) => ({
			value: client.id,
			label: `${client.person.name} ${client.person.fatherLastName} ${client.person.motherLastName}`,
		})) || [];

	const workerOptions_data =
		workers?.map((worker) => ({
			value: worker.id,
			label: `${worker.person.name} ${worker.person.fatherLastName} ${worker.person.motherLastName}`,
		})) || [];

	const handleClientChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const client = clients?.find((c) => c.id === option.value);
			sessionActions.setClient(client || null);
		} else {
			sessionActions.setClient(null);
		}
	};

	const handleWorkerChange = (
		option: { value: string | number; label: string } | null,
	) => {
		if (option) {
			const worker = workers?.find((w) => w.id === option.value);
			sessionActions.setWorker(worker || null);
		} else {
			sessionActions.setWorker(null);
		}
	};

	const selectedClientOption = selectedClient
		? {
				value: selectedClient.id,
				label: `${selectedClient.person.name} ${selectedClient.person.fatherLastName} ${selectedClient.person.motherLastName}`,
				data: selectedClient,
			}
		: null;

	const selectedWorkerOption = selectedWorker
		? {
				value: selectedWorker.id,
				label: `${selectedWorker.person.name} ${selectedWorker.person.fatherLastName} ${selectedWorker.person.motherLastName}`,
				data: selectedWorker,
			}
		: null;

	return (
		<div className="grid grid-cols-2 gap-4">
			{/* Client ComboBox */}
			<div>
				{clientsError ? (
					<div className="alert alert-error text-xs">
						<span>Error: {getErrorResult(clientError).error.message}</span>
					</div>
				) : (
					<ComboBox
						options={clientOptions_data}
						value={selectedClientOption}
						onChange={handleClientChange}
						placeholder="Buscar cliente..."
						isLoading={clientsPending}
						label="Cliente"
						className="w-full"
					/>
				)}
			</div>

			{/* Worker ComboBox */}
			<div>
				{workersError ? (
					<div className="alert alert-error text-xs">
						<span>Error: {getErrorResult(workerError).error.message}</span>
					</div>
				) : (
					<ComboBox
						options={workerOptions_data}
						value={selectedWorkerOption}
						onChange={handleWorkerChange}
						placeholder="Buscar trabajador..."
						isLoading={workersPending}
						label="Trabajador"
						className="w-full"
					/>
				)}
			</div>
		</div>
	);
}
