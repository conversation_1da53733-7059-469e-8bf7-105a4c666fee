import { <PERSON>ttpBody } from "@effect/platform";
import { <PERSON>, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import { SessionRepository } from "../../model/repository";
import type { CreateSession, UpdateSession } from "../../model/session";
import {
	CreateSessionApiFromCreateSession,
	CreateSessionApiResponse,
	UpdateSessionApiFromUpdateSession,
	SessionFromApi,
	SessionListFromApi,
} from "./dto";

const baseUrl = "/v1/sessions";

const makeSessionApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(SessionListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(SessionFromApi))),
		getByClientAndTurn: (clientId: string, turnId: string) =>
			httpClient
				.get(`${baseUrl}/by-client-and-turn?client_id=${clientId}&turn_id=${turnId}`)
				.pipe(Effect.flatMap(handleDResponse(SessionListFromApi))),
		getByWorkerAndTurn: (workerId: string, turnId: string) =>
			httpClient
				.get(`${baseUrl}/by-worker-and-turn?worker_id=${workerId}&turn_id=${turnId}`)
				.pipe(Effect.flatMap(handleDResponse(SessionListFromApi))),
		create: (session: CreateSession) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateSessionApiFromCreateSession)(session),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateSessionApiResponse))),
		update: (session: UpdateSession) =>
			httpClient
				.put(`${baseUrl}/${session.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateSessionApiFromUpdateSession)(session),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const sessionApiRepoLive = Layer.effect(
	SessionRepository,
	makeSessionApiRepo,
);
