import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { CreateSession, UpdateSession, Session } from "./session";

export class SessionUsecase extends Effect.Tag("SessionUsecase")<
	SessionUsecase,
	{
		readonly getAll: () => Effect.Effect<Session[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Session, AppError>;
		readonly getByClientAndTurn: (clientId: string, turnId: string) => Effect.Effect<Session[], AppError>;
		readonly getByWorkerAndTurn: (workerId: string, turnId: string) => Effect.Effect<Session[], AppError>;
		readonly create: (session: CreateSession) => Effect.Effect<string, AppError>;
		readonly update: (session: UpdateSession) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
