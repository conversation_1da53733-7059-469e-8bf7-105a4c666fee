import { Schema } from "effect";
import { Client } from "~/client/service/model/client";
import { Worker } from "~/worker/service/model/worker";

export const Session = Schema.Struct({
	id: Schema.String,
	client: Client,
	worker: Worker,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Session = typeof Session.Type;

export const CreateSession = Schema.Struct({
	clientId: Schema.String,
	workerId: Schema.String,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
});
export type CreateSession = typeof CreateSession.Type;

export const UpdateSession = Schema.Struct({
	id: Schema.String,
	clientId: Schema.String,
	workerId: Schema.String,
	turnId: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
});
export type UpdateSession = typeof UpdateSession.Type;
