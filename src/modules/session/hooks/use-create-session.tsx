import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CreateSession, Session } from "../service/model/session";
import { sessionOptions, sessionOptionsByClientAndTurn, sessionOptionsByWorkerAndTurn } from "./session-options";

export default function useCreateSession() {
	const service = useService();
	const { session } = service;
	const queryClient = useQueryClient();
	const queryKey = sessionOptions(service).queryKey;

	return useMutation({
		mutationFn: (newSession: CreateSession) =>
			AppRuntime.runPromise(session.create(newSession)),
		onSuccess: (sessionId, variables) => {
			queryClient.setQueryData(queryKey, (old: Session[] | undefined) =>
				create(old ?? [], (draft) => {
					// Note: We would need the full session object to add it to the cache
					// For now, we'll just invalidate the query to refetch
				}),
			);
			// Invalidate all session-related queries
			queryClient.invalidateQueries({ queryKey });
			queryClient.invalidateQueries({
				queryKey: sessionOptionsByClientAndTurn(service, variables.clientId, variables.turnId).queryKey
			});
			queryClient.invalidateQueries({
				queryKey: sessionOptionsByWorkerAndTurn(service, variables.workerId, variables.turnId).queryKey
			});
		},
	});
}
