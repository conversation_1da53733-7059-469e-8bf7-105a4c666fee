import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const sessionOptions = ({ session }: serviceRegistry) =>
	queryOptions({
		queryKey: ["sessions"],
		queryFn: () => AppRuntime.runPromise(session.getAll()),
	});

export const sessionOptionsById = ({ session }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["sessions", id],
		queryFn: () => AppRuntime.runPromise(session.getById(id)),
	});

export const sessionOptionsByClientAndTurn = (
	{ session }: serviceRegistry,
	clientId: string,
	turnId: string,
) =>
	queryOptions({
		queryKey: ["sessions", "by-client-and-turn", clientId, turnId],
		queryFn: () => AppRuntime.runPromise(session.getByClientAndTurn(clientId, turnId)),
		enabled: !!clientId && !!turnId,
	});

export const sessionOptionsByWorkerAndTurn = (
	{ session }: serviceRegistry,
	workerId: string,
	turnId: string,
) =>
	queryOptions({
		queryKey: ["sessions", "by-worker-and-turn", workerId, turnId],
		queryFn: () => AppRuntime.runPromise(session.getByWorkerAndTurn(workerId, turnId)),
		enabled: !!workerId && !!turnId,
	});
